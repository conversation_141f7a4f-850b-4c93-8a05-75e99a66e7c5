package adhoc.helper

import com.decurret_dcp.dcjpy.bcmonitoring.config.ContextConfig
import org.testcontainers.containers.DockerComposeContainer
import org.testcontainers.containers.wait.strategy.Wait
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*

class AdhocHelper {

	private static localStackPort

	private static DockerComposeContainer composeContainer
	public static final String LOCALSTACK = "localstack"

	static String getLocalStackPort() {
		return localStackPort
	}

	static {
		startContainer()
	}

	private static void startContainer() {
		composeContainer = new DockerComposeContainer(new File("docker-compose-test.yml"))
				.withExposedService(LOCALSTACK, 4566)
				.waitingFor(LOCALSTACK, Wait.forListeningPort())

		composeContainer.start()
		// Get the actual mapped port from the container
		localStackPort = composeContainer.getServicePort(LOCALSTACK, 4566).toString()
		println("LocalStack started successfully on port: " + localStackPort)
	}

	static void cleanupSpec() {
		//To do cleanup Spec
	}

	static boolean tableExists(DynamoDbClient dynamoDbClient, String tableName) {
		println("Checking if table exists: " + tableName)
		DescribeTableRequest request = DescribeTableRequest.builder()
				.tableName(tableName)
				.build() as DescribeTableRequest
		try {
			dynamoDbClient.describeTable(request)
			println("Table exists: " + tableName)
			return true
		} catch (ResourceNotFoundException ignored) {
			println("Table does not exist: " + tableName)
			return false
		} catch (Exception e) {
			println("Error checking table existence: " + e.getMessage())
			e.printStackTrace()
			return false
		}
	}

	static void createEventsTable(DynamoDbClient dynamoDbClient, String tableName) {
		if (!tableExists(dynamoDbClient, tableName)) {
			CreateTableRequest createTableRequest = CreateTableRequest.builder()
					.tableName(tableName)
					.keySchema(
					KeySchemaElement.builder().attributeName("transactionHash").keyType(KeyType.HASH).build(),
					KeySchemaElement.builder().attributeName("logIndex").keyType(KeyType.RANGE).build()
					)
					.attributeDefinitions(
					AttributeDefinition.builder().attributeName("transactionHash").attributeType(ScalarAttributeType.S).build(),
					AttributeDefinition.builder().attributeName("logIndex").attributeType(ScalarAttributeType.N).build()
					)
					.provisionedThroughput(ProvisionedThroughput.builder().readCapacityUnits(5).writeCapacityUnits(5).build())
					.build() as CreateTableRequest

			dynamoDbClient.createTable(createTableRequest)
		}
	}

	static void createBlockHeightTable(DynamoDbClient dynamoDbClient, String tableName) {
		println("Attempting to create table: " + tableName)
		if (!tableExists(dynamoDbClient, tableName)) {
			println("Table does not exist, creating: " + tableName)
			CreateTableRequest createTableRequest = CreateTableRequest.builder()
					.tableName(tableName)
					.keySchema(
					KeySchemaElement.builder().attributeName("id").keyType(KeyType.HASH).build()
					)
					.attributeDefinitions(
					AttributeDefinition.builder().attributeName("id").attributeType(ScalarAttributeType.N).build()
					)
					.provisionedThroughput(ProvisionedThroughput.builder().readCapacityUnits(5).writeCapacityUnits(5).build())
					.build() as CreateTableRequest

			try {
				dynamoDbClient.createTable(createTableRequest)
				println("Table created successfully: " + tableName)
			} catch (Exception e) {
				println("Error creating table: " + e.getMessage())
				e.printStackTrace()
			}
		} else {
			println("Table already exists: " + tableName)
		}
	}

	static boolean bucketExists(S3Client s3Client, String bucketName) {
		println("Checking if bucket exists: " + bucketName)
		try {
			def request = HeadBucketRequest.builder()
					.bucket(bucketName)
					.build() as HeadBucketRequest

			s3Client.headBucket(request)
			println("Bucket exists: " + bucketName)
			return true
		} catch (NoSuchBucketException ignored) {
			println("Bucket does not exist: " + bucketName)
			return false
		} catch (Exception e) {
			println("Error checking bucket existence: " + e.getMessage())
			e.printStackTrace()
			return false
		}
	}

	static void createS3Bucket(S3Client s3Client, String bucketName) {
		println("Attempting to create bucket: " + bucketName)
		if (!bucketExists(s3Client, bucketName)) {
			println("Bucket does not exist, creating: " + bucketName)
			try {
				def request = CreateBucketRequest.builder()
						.bucket(bucketName)
						.build() as CreateBucketRequest
				s3Client.createBucket(request)
				println("Bucket created successfully: " + bucketName)
			} catch (Exception e) {
				println("Error creating bucket: " + e.getMessage())
				e.printStackTrace()
			}
		} else {
			println("Bucket already exists: " + bucketName)
		}
	}

	/**
	 * Generic method to upload ABI files to S3 bucket with configurable source directory and key pattern
	 * @param s3Client The S3 client to use for uploading
	 * @param bucketName The target S3 bucket name
	 * @param sourceDir The source directory path relative to src/test/groovy/adhoc/resources/abi_json/
	 * @param networkId The network ID (e.g., "3000", "3001")
	 * @param contractNames List of contract names to upload (e.g., ["Token", "Account"])
	 * @param keyPattern The S3 key pattern (e.g., "{networkId}/{fileName}" or "{networkId}/nested_directory/{fileName}")
	 * @param fileExtension The file extension to filter (default: ".json")
	 * @param addExtensionToContractNames Whether to add file extension to contract names (default: true)
	 */
	static void uploadAbiFiles(S3Client s3Client, String bucketName, String sourceDir, String networkId,
			List<String> contractNames = null, String keyPattern = "{networkId}/{fileName}",
			String fileExtension = ".json", boolean addExtensionToContractNames = true) {
		println("Uploading ABI files from ${sourceDir} for network: ${networkId} to bucket: ${bucketName}")

		def abiDataDir = new File("src/test/groovy/adhoc/resources/abi_json/${sourceDir}")
		if (!abiDataDir.exists()) {
			println("Warning: ABI data directory does not exist: ${abiDataDir.absolutePath}")
			return
		}

		def filesToUpload
		if (contractNames) {
			filesToUpload = addExtensionToContractNames ?
					contractNames.collect { "${it}${fileExtension}" } :
					contractNames
		} else {
			filesToUpload = abiDataDir.listFiles({ file -> file.name.endsWith(fileExtension) } as FileFilter)*.name
		}

		filesToUpload.each { fileName ->
			def abiFile = new File(abiDataDir, fileName.toString())
			if (abiFile.exists()) {
				def key = keyPattern.replace("{networkId}", networkId).replace("{fileName}", fileName.toString())
				def content = abiFile.text
				println("Uploading ABI file: ${key} (${content.length()} bytes)")

				s3Client.putObject(PutObjectRequest.builder()
						.bucket(bucketName)
						.key(key)
						.build() as PutObjectRequest,
						RequestBody.fromString(content))
			} else {
				println("Warning: ABI file not found: ${abiFile.absolutePath}")
			}
		}
	}

	/**
	 * Upload Hardhat ABI files from src/test/groovy/adhoc/resources/abi_json/hardhat directory to test S3 bucket
	 * @param s3Client The S3 client to use for uploading
	 * @param bucketName The target S3 bucket name
	 * @param networkId The network ID (e.g., "3000", "3001")
	 * @param contractNames List of contract names to upload (e.g., ["Token", "Account"])
	 */
	static void uploadHardhatAbiFiles(S3Client s3Client, String bucketName, String networkId, List<String> contractNames = null) {
		uploadAbiFiles(s3Client, bucketName, "hardhat/${networkId}", networkId, contractNames)
	}

	static void uploadRealAbiFilesWrongPrefix(S3Client s3Client, String bucketName, String networkId, List<String> contractNames = null) {
		def abiDataDir = new File("src/test/groovy/adhoc/json/${networkId}")
		if (!abiDataDir.exists()) {
			println("Warning: ABI data directory does not exist: ${abiDataDir.absolutePath}")
			return
		}

		def filesToUpload = contractNames ?
				contractNames.collect { "${it}.json" } :
				abiDataDir.listFiles({ file -> file.name.endsWith('.json') } as FileFilter)*.name

		filesToUpload.each { fileName ->
			def abiFile = new File(abiDataDir, fileName.toString())
			if (abiFile.exists()) {
				def key = "${networkId}+${fileName}"
				def content = abiFile.text
				println("Uploading real ABI file: ${key} (${content.length()} bytes)")

				s3Client.putObject(PutObjectRequest.builder()
						.bucket(bucketName)
						.key(key)
						.build() as PutObjectRequest,
						RequestBody.fromString(content))
			} else {
				println("Warning: ABI file not found: ${abiFile.absolutePath}")
			}
		}
	}

	/**
	 * Upload Truffle ABI files from truffle directory to test S3 bucket
	 * @param s3Client The S3 client to use for uploading
	 * @param bucketName The target S3 bucket name
	 * @param networkId The network ID (e.g., "3000", "3001")
	 * @param contractNames List of contract names to upload (e.g., ["Token", "Account"])
	 */
	static void uploadTruffleAbiFiles(S3Client s3Client, String bucketName, String networkId, List<String> contractNames = null) {
		uploadAbiFiles(s3Client, bucketName, "truffle/3000", networkId, contractNames)
	}

	/**
	 * Upload invalid ABI files for testing error scenarios (non-JSON or malformed files)
	 * @param s3Client The S3 client to use for uploading
	 * @param bucketName The target S3 bucket name
	 * @param networkId The network ID (e.g., "3000", "3001")
	 * @param contractNames List of full file names to upload (e.g., ["nonJson.txt", "MalformedJson.json"])
	 */
	static void uploadInvalidAbiFiles(S3Client s3Client, String bucketName, String networkId, List<String> contractNames = null) {
		// For invalid files, we don't add extension and use any file extension
		uploadAbiFiles(s3Client, bucketName, "invalid", networkId, contractNames, "{networkId}/{fileName}", "", false)
	}

	/**
	 * Upload ABI files with nested directory structure for testing deep nesting scenarios
	 * @param s3Client The S3 client to use for uploading
	 * @param bucketName The target S3 bucket name
	 * @param networkId The network ID (e.g., "3000", "3001")
	 * @param contractNames List of contract names to upload (e.g., ["FinancialCheck"])
	 */
	static void uploadAbiFilesWithNestedPath(S3Client s3Client, String bucketName, String networkId, List<String> contractNames = null) {
		uploadAbiFiles(s3Client, bucketName, "hardhat/${networkId}", networkId, contractNames, "{networkId}/nested_directory/{fileName}")
	}

	static void resetRunningFlag() {
		ContextConfig.setServiceRunning(true)
		println("Reset running flag to true")
	}

	static void stopBCMonitoring() {
		ContextConfig.setServiceRunning(false)
	}
}
